# Hello world!

If you are reading this, you probably need a help hand. Well, that sucks. For local development,
I run this up simply by running it with these env variable and quarkus property.

- `UNITE_SYSTEM_ROLE` dev   #ENV variable
- `quarkus.profile` dev     #Quarkus standard property   

Easiest way to do it is via CLI arguments to the Quarkus app:
- `UNITE_SYSTEM_ROLE=dev mvn quarkus:dev -Dquarkus.profile=dev`


There are a few diagrams in [this repo](docs) to help visualise and understand:
* Deployment process, see [`infrastructure.drawio`](docs/infrastructure.drawio) and upload it here: https://app.diagrams.net/.
* HL architecture overview, see [`architecture.drawio`](docs/architecture.drawio) in this repo too.
* Data architecture, see [`data-architecture.drawio`](docs/data-architecture.drawio).
* Detailed DB Schema, see [`unite-schema.dbml`](docs/unite-schema.dbml) and paste it here: https://dbdiagram.io/.

The following readme is not done by me, but it could be helpful as well. I don't use it. 
Just remember, use **Java 21**. I use **Maven 3.8.7** and it works well.

If you are running it on your personal computer, you have to be logged in to VPN, because MSSQL connection
is not working beyond the borders of Hamjet network as well as LDAP login. I recommend to hack [`LoginService`](src/main/java/unite/nz/hamiltonjet/service/LoginService.java) and
add your username as and additional condition. Good luck! Matej Seifert
Also, make sure you have a record in the DB table: `unite_user` for each env. you'd like to access it.

# CI/CD
More details how to access it and make use of it is in another repo: [Unite-server-settings](https://github.com/HamiltonJet/Unite-server-settings/).
Jenkins and Docker sit at `*************` VM. 

## Environments [DEV, TEST & PROD]
This project utilizes three main application/development environments: **DEV**, **TEST**, and **PROD**.
- Each environment is equipped with its own specific MS SQL database.
- There are two **M3 Integration Endpoints** shared across these environments.

**M3 ERP System Sync (Infor Cloud Managed):**
- The M3 ERP system, which our environments integrate with, is managed by Infor Cloud.
- For details on how the M3 PROD version is synchronized with the TRN (Training) version (which supports our DEV & TEST environments), please contact **Dominic Feran**.

**Unite Database Sync (PROD/DEV/TEST):**
To maintain data and schema consistency across the Unite application databases in these environments:
- A script developed by **Tony Cook**, titled "**Sync PROD/DEV/TEST Unite DB Schema and Data**," is utilized for this purpose.
- **How to Request a Sync**: If a synchronization or update of the environments is necessary, please request it by emailing `<EMAIL>`. This will create a ticket for the IT department.
- **Automation Status**: While this process is intended for regular execution to keep environments aligned, it is not yet automated.

# Unite Quarkus project
Its **Quarkus JSF**

In IDE load and you can use:

- Use any IDE tooling (JBoss tools for running the quarkus works well in MyEclipseIDE as its included by default).

-    Run the [`Main`](src/main/java/unite/nz/hamiltonjet/Main.java) class in project as **"java application"**.
-    <details>
        <summary>In IntelliJ IDEA, you may need to update/create "Quarkus Run Configuration" like this:</summary>

        ![img.png](docs/IDEQuarkusRunConf.png)
    </details>
-    Maven to run the Quarkus project
-    Don't forget to setup CLI arguments to the JVM and Env variables in your *Run configuration* in the IDE. 
I worked my way through all these :)

Create a default user and see Login service to set yourself up as Admin (or hack DB), if you need admin.

# Quarkus

This project uses Quarkus, the Supersonic Subatomic Java Framework.

If you want to learn more about Quarkus, please visit its website: https://quarkus.io/ .

## Running the application in dev mode

You can run your application in dev mode that enables live coding using:
* 
    ```shell script
        UNITE_SYSTEM_ROLE=dev ./mvnw compile quarkus:dev -Dquarkus.profile=dev
     ```
* Or, if the variable & property has been set elsewhere, this should work:
    ```shell script
        ./mvnw compile quarkus:dev
    ```

> **_NOTE:_**  Quarkus now ships with a [Dev UI](https://quarkus.io/guides/dev-ui), which is available in dev mode only at http://localhost:5002/q/dev/.

## Packaging and running the application

The application can be packaged using:
```shell script
./mvnw package
```
It produces the `quarkus-run.jar` file in the `target/quarkus-app/` directory.
Be aware that it’s not an _über-jar_ as the dependencies are copied into the `target/quarkus-app/lib/` directory.

If you want to build an _über-jar_, execute the following command:
```shell script
./mvnw package -Dquarkus.package.type=uber-jar
```
> **_NOTE:_**  for Windows, you may need to put quotes for args: `-D"quarkus.package.type=uber-jar"`<br>
> **_NOTE2:_**  If you're using WSL for your development on Windows, then make sure you can access internal WSL network interfaces and ports on the Windows host, 
> i.e. by **"Port Forwarding"** running a provided shell script [wsl-port-forwarding.sh](wsl-port-forwarding.sh) from WSL (don't use the IDE terminal).

The application is now runnable using `java -jar target/quarkus-app/quarkus-run.jar`.

## Creating a native executable

You can create a native executable using: 
```shell script
./mvnw package -Pnative
```

Or, if you don't have GraalVM installed, you can run the native executable build in a container using: 
```shell script
./mvnw package -Pnative -Dquarkus.native.container-build=true
```
> **_NOTE:_**  for Windows, you may need to put quotes for args: -D"quarkus.native.container-build=true"

You can then execute your native executable with: `./target/code-with-quarkus-1.0.0-SNAPSHOT-runner`

If you want to learn more about building native executables, please consult https://quarkus.io/guides/maven-tooling.html.

## Provided Code

### RESTEasy JAX-RS

Easily start your RESTful Web Services

[Related guide section...](https://quarkus.io/guides/getting-started#the-jax-rs-resources)

## Unit Testing
There are not many JUnit Tests atm, but all the project has been configured to use
* [JUnit 5](https://junit.org/junit5/) - the latest version of a test automation framework
* [Mockito](https://site.mockito.org/) - a popular Java mocking framework for unit testing

Example Tests are:
* [M3DataStoreServiceTest](src/test/java/unite/nz/hamiltonjet/service/m3/M3DataStoreServiceTest.java) - mainly to cover functionality of `getMatrixShipset()` method
* [STest](src/test/java/unite/nz/hamiltonjet/utils/STest.java) - for pattern matching for the Matrix functionality

Integrating Mockito into Quarkus FM was not straightforward and IntelliJ IDEA is having problems to execute them.
You can run them from CLI with:
```shell script
./mvnw test   ## to run all test
 mvn test -Dtest=unite.nz.hamiltonjet.utils.STest   ## to run specific test
 mvn test -Dtest=unite.nz.hamiltonjet.service.m3.M3DataStoreServiceTest
```
or you need to **Delegate your IDE build/run actions to Maven**.
<details>
<summary>IDE to Maven delegation screenshot</summary>

![img.png](docs/IDE2Maven.png)
</details>

Also, if you'd like to avoid seeing many warnings when running Mockito JUnit tests from the IDE, you need to edit your **Run configuration**
and set the VM params to the following:
<details>
<summary>IDE Edit Configuration VM params screenshot</summary>

![img.png](docs/IDEVMArgs.png)
</details>

```
-ea
-javaagent:\\wsl.localhost\UbuntuUnite\home\delphym\.m2\repository\org\mockito\mockito-core\5.18.0\mockito-core-5.18.0.jar
-Djava.util.logging.manager=org.jboss.logmanager.LogManager
-XX:+EnableDynamicAgentLoading
```
