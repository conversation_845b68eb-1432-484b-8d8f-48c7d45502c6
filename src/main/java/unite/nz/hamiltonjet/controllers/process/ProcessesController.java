package unite.nz.hamiltonjet.controllers.process;

import jakarta.annotation.PostConstruct;
import jakarta.faces.application.FacesMessage;
import jakarta.faces.context.FacesContext;
import jakarta.faces.model.SelectItem;
import jakarta.faces.view.ViewScoped;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import jakarta.transaction.Transactional;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.jboss.logging.Logger;
import org.primefaces.PrimeFaces;
import org.primefaces.event.RowEditEvent;
import unite.nz.hamiltonjet.controllers.security.interceptors.Authenticated;
import unite.nz.hamiltonjet.controllers.security.interceptors.Authorised;
import unite.nz.hamiltonjet.entity.enums.ApplicationRole;
import unite.nz.hamiltonjet.entity.enums.JetSizeKind;
import unite.nz.hamiltonjet.entity.enums.ProcessType;
import unite.nz.hamiltonjet.entity.process.Process;
import unite.nz.hamiltonjet.entity.process.SlotboardProcess;
import unite.nz.hamiltonjet.service.process.SlotboardProcessService;
import unite.nz.hamiltonjet.utils.SerialVersionGenerator;

import java.io.Serial;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Named
@ViewScoped
@Authenticated
@Authorised(ApplicationRole.USER)
public class ProcessesController implements Serializable {

    @Serial
    private static final long serialVersionUID = SerialVersionGenerator.get();

    @Inject
    Logger logger;

    @Inject
    SlotboardProcessService processService; // Use your concrete service

    @Getter @Setter
    private JetSizeKind selectedJetSizeKind = JetSizeKind.LARGE;

    @Getter @Setter
    private ProcessType selectedProcessType;

    @Getter
    private List<SelectItem> processChoices = new ArrayList<>();

    @Getter
    private List<SlotboardProcess> processesForSelectedType = new ArrayList<>();

    @Getter
    private List<ProcessStep> chosenProcessSteps = new ArrayList<>();

    @Getter @Setter
    private ProcessStep pendingActionStep;
    @Getter @Setter
    private String originalStepName, newStepName, dialogNewStepName, mergeTargetStepName;

    @PostConstruct
    @Transactional
    public void init() {
        logger.debug("> init ProcessesController");
        loadProcessTypeChoices();
        logger.debug("< init ProcessesController");
    }

    // Listeners for jet size, process type, and row changes changes
    public void jetSizeChangeListener() {
        logger.debugf("Jet size changed to: %s", selectedJetSizeKind);

        // Clear current selections
        selectedProcessType = null;
        processesForSelectedType = new ArrayList<>();
        chosenProcessSteps = new ArrayList<>();    // Clear chosen process steps

        // Relead process choices for the new jet size
        loadProcessTypeChoices();
    }

    public void selectedProcessTypeListener() {
        logger.debugf("Process type selection changed to: %s", selectedProcessType);
        processesForSelectedType = new ArrayList<>();
        chosenProcessSteps = new ArrayList<>();    // Clear chosen process steps when type changes

        if (selectedProcessType != null) {
            // Get all processes for the selected jet size and filter by process type
            List<SlotboardProcess> allProcesses = this.getProcessesForJetSize(selectedJetSizeKind);
            processesForSelectedType = allProcesses.stream()
                    .filter(p -> p.getProcessType().equals(selectedProcessType))
                    .sorted(Comparator.comparingInt(Process::getOrderNumber))
                    .collect(Collectors.toList()); // This creates a mutable ArrayList

            logger.debugf("Found %d process for type %s.", processesForSelectedType.size(), selectedProcessType);
        }

        // Create process steps ONCE when process type changes
        createProcessSteps();
    }

    // Creates process steps based on the selected process and store them
    private void createProcessSteps() {
        chosenProcessSteps.clear();

        for (int i = 0; i < processesForSelectedType.size(); i++) {
            SlotboardProcess process = processesForSelectedType.get(i);
            ProcessStep step = new ProcessStep();
            step.setName(process.getName());
            step.setOrderNumber(process.getOrderNumber());
            step.setStepId("step_" + process.getId() + "_" + i);    // Unique ID for each step

            logger.infof("Creating step with ID: %s for process: %s", step.getStepId(), step.getName());

            // Set merge/duplicate capabilities based on position in the process
            step.setCanMergePrevious(i > 0); // Can merge if not the first step
            step.setCanDuplicate(true); // All steps can be duplicated for now
            step.setCanDelete(true); // All steps can be deleted for now

            // Set some default rates - you may want to make these configurable
            step.setMergeBelowBucketRate(50);
            step.setDuplicateAboveBucketRate(80);
            chosenProcessSteps.add(step);
        }
    }

    public void onRowEdit(RowEditEvent<SlotboardProcess> event) {
        SlotboardProcess editedProcess = event.getObject();
        logger.debugf("Row selection changed to: %s", editedProcess.getName());

        try {
            // Save the edited process to DB
            // TODO inject processService and call save method
            // processService.saveOrUpdate(editedProcess);

            FacesMessage msg = new FacesMessage("Process updated", "Process: " + editedProcess.getName());
            FacesContext.getCurrentInstance().addMessage(null, msg);
        } catch (Exception e) {
            logger.errorf("Error updating process: %s", e.getMessage());
            FacesMessage msg = new FacesMessage(FacesMessage.SEVERITY_ERROR, "Update of the process failed", e.getMessage());
            FacesContext.getCurrentInstance().addMessage(null, msg);
        }
    }

    public void onRowCancel(RowEditEvent<SlotboardProcess> event) {
        FacesMessage msg = new FacesMessage("Edit Cancelled", "Edit of process: " + event.getObject().getName() + " cancelled");
        FacesContext.getCurrentInstance().addMessage(null, msg);
    }

    private void loadProcessTypeChoices() {
        processChoices.clear();
        processesForSelectedType.clear();
        chosenProcessSteps.clear();

        // Get ALL process types for the selected jet size
        List<SlotboardProcess> allProcesses = this.getProcessesForJetSize(selectedJetSizeKind);

        logger.debugf("Loading process choices for %s jets. Found %d total processes", selectedJetSizeKind, allProcesses.size());

        // Get distinct process types for the selected jet size
        List<ProcessType> distinctProcessTypes = allProcesses.stream()
            .map(SlotboardProcess::getProcessType)
            .distinct()
            .sorted()
            .collect(Collectors.toList()); // Use collect instead of toList()

        // Create select items for each distinct process type
        for (ProcessType processType : distinctProcessTypes) {
            SelectItem item = new SelectItem(processType,
                processType.name());
            processChoices.add(item);
        }
        
        logger.debugf("Loaded %d process choices for %s jets", getProcessChoices().size(), selectedJetSizeKind);
    }

    public List<SlotboardProcess> getProcessesForJetSize(JetSizeKind jetSizeKind) {
        return processService.getAllProcesses().stream()
            .filter(p -> p.getJetSizeKind().equals(jetSizeKind))
            .sorted(Comparator.comparingInt(Process::getOrderNumber))
            .collect(Collectors.toList());
    }

    // Find step by ID to ensure we're working with the right one
    private ProcessStep findStepById(String stepId) {
        return getChosenProcessSteps().stream()
            .filter(step -> step.getStepId().equals(stepId))
            .findFirst()
            .orElse(null);
    }

    // Listeners for checkboxes
    public void setMerge(ProcessStep step) {
        logger.infof("Merge toggled for step: %s, value: %s", step.getName(), step.isDoMerge());

        // Find the actual step in our list to ensure state persistence
        ProcessStep actualStep = findStepById(step.getStepId());

        // Clear other checkboxes for this step only if this one is being selected
        if (actualStep != null) {
            logger.debug(actualStep);
            if (actualStep.isDoMerge()) {
                actualStep.setDoDuplicate(false);
                actualStep.setDoDelete(false);
                // TODO: Show a dialog to confirm the MERGE action
                logger.infof("Expecting merge confirmation dialog for step: %s. User should confirm.", actualStep.getName());
                this.confirmMerge(actualStep);
            }
        } else {
            logger.errorf("Could not find step with ID %s during MERGE operation. ", step.getStepId());
        }

        // TODO Implement merge logic here
        // You might want to update some database state or perform other actions
    }
    public void setDuplicate(ProcessStep step) {
        logger.infof("Duplicate toggled for step: %s, value: %s", step.getName(), step.isDoDuplicate());

        // Find the actual step in our list to ensure state persistence
        ProcessStep actualStep = findStepById(step.getStepId());

        // Clear other checkboxes for this step only if this one is being checked
        if (actualStep != null) {
            logger.debug(actualStep);
            if (actualStep.isDoDuplicate()) {
                actualStep.setDoMerge(false);
                actualStep.setDoDelete(false);
                // TODO: Show a dialog to get new names for both duplicated steps
                logger.infof("Expecting duplicate confirmation dialog for step: %s. User should confirm.", actualStep.getName());
                this.confirmDuplicate(actualStep);
            }
        } else {
            logger.errorf("Could not find step with ID %s during DUPLICATE operation. ", step.getStepId());
        }

        // TODO Implement duplicate logic here
        // You might want to update some database state or perform other actions
    }
    public void setDelete(ProcessStep step) {
        logger.infof("Delete toggled for step: %s, value: %s", step.getName(), step.isDoDelete());

        // Find the actual step in our list to ensure state persistence
        ProcessStep actualStep = findStepById(step.getStepId());

        // Clear other checkboxes for this step only if this one is being checked
        if (actualStep != null) {
            logger.debug(actualStep);
            if (actualStep.isDoDelete()) {
                logger.infof("Delete action selected for step: %s", step.getName());
                actualStep.setDoMerge(false);
                actualStep.setDoDuplicate(false);
                // Trigger the confirmation dialog
                logger.infof("Expecting delete confirmation dialog for step: %s. User should confirm.", actualStep.getName());
                this.confirmDelete(actualStep);
            }
        } else {
            logger.errorf("Could not find step with ID %s during DELETE operation. ", step.getStepId());
        }

        // TODO Implement deletion logic here
        // You might want to update some database state or perform other actions
    }

    // Confirmation methods for each action to handle confirmation dialogs
    public void confirmMerge(@NotNull ProcessStep step) {
        ProcessStep actualStep = findStepById(step.getStepId());
        if (actualStep != null && actualStep.isDoMerge()) {
            logger.infof("Merge action confirmed for step: %s", actualStep.getName());
            // Find the previous steps for merging
            int currentIndex = chosenProcessSteps.indexOf(actualStep);
            if (currentIndex > 0) {
                ProcessStep previousStep = chosenProcessSteps.get(currentIndex - 1);
                this.setMergeTargetStepName(previousStep.getName());

                // Store the step for the actual merge
                this.setPendingActionStep(actualStep);
                this.setNewStepName(this.getMergeTargetStepName() + " & " + this.getPendingActionStep().getName());
                this.setDialogNewStepName(this.getNewStepName());

                // Show confirmation dialog
                PrimeFaces.current().executeScript("PF('mergeConfirmDialog').show();");
                PrimeFaces.current().ajax().update("processShowForm:mergeConfirmDialog");
            }
        } else {
            logger.errorf("Could not find step with ID %s during MERGE confirmation. ", step.getStepId());
        }

        // TODO: Implement merge confirmation logic
        // This could involve showing a dialog to the user to confirm the merge action
        // and then performing the merge if the user confirms
    }
    public void confirmDuplicate(@NotNull ProcessStep step) {
        // TODO: Implement duplicate confirmation logic
        // This could involve showing a dialog to the user to confirm the duplicate action
        // and then performing the duplicate if the user confirms
        ProcessStep actualStep = findStepById(step.getStepId());
        if (actualStep != null && actualStep.isDoDuplicate()) {
            logger.infof("Duplicate action confirmed for step: %s", actualStep.getName());
            // Set default names for the dialog
            this.setOriginalStepName(actualStep.getName());
            this.setNewStepName(actualStep.getName() + " (Copy)");

            // Store the step for the actual duplication
            this.setPendingActionStep(actualStep);

            // Show confirmation dialog
            PrimeFaces.current().executeScript("PF('duplicateConfirmDialog').show();");
            PrimeFaces.current().ajax().update("processShowForm:duplicateConfirmDialog");
        } else {
            logger.errorf("Could not find step with ID %s during DUPLICATE confirmation. ", step.getStepId());
        }
    }
    public void confirmDelete(@NotNull ProcessStep step) {
        // TODO: Implement delete confirmation logic
        // This could involve showing a dialog to the user to confirm the delete action
        // and then performing the delete if the user confirms
        ProcessStep actualStep = findStepById(step.getStepId());
        if (actualStep != null && actualStep.isDoDelete()) {
            logger.infof("Delete action confirmed for step: %s", actualStep.getName());
            // Store the step for the actual deletion
            this.setPendingActionStep(actualStep);

            // Show confirmation dialog
            PrimeFaces.current().executeScript("PF('deleteConfirmDialog').show();");
            PrimeFaces.current().ajax().update("processShowForm:deleteConfirmDialog");
        } else {
            logger.errorf("Could not find step with ID %s during DELETE confirmation. ", step.getStepId());
        }
    }

    // Actual BL methods for each action (merge, duplicate, delete) called after confirmation
    public void executeMerge() {
        logger.infof("Merge action executed for step: %s", this.getPendingActionStep().getName());
        if (pendingActionStep != null && this.getMergeTargetStepName() != null && !this.getMergeTargetStepName().trim().isEmpty()) {
            logger.infof("Execution MERGE for step: %s with target: %s", pendingActionStep.getName(), this.getMergeTargetStepName());
            logger.debugf("XXXX New (merged) step name: %s", this.getNewStepName());
            logger.debugf("YYYYY Dialog new step name: %s", this.getDialogNewStepName());

            // TODO: Implement merge logic here
            // 1. Combining the steps
            // 2. Updating the order numbers of the subsequent steps
            // 3. Updating the database

            int currentIndex = chosenProcessSteps.indexOf(pendingActionStep);
            if (currentIndex > 0 ) {
                ProcessStep previousStep = chosenProcessSteps.get(currentIndex - 1);

                // Merge names
//                previousStep.setName(previousStep.getName().trim() + " & " + pendingActionStep.getName());
                logger.debugf("Merging steps: %s with target: %s", this.getPendingActionStep().getName(), this.getMergeTargetStepName());
                logger.debugf("New (merged) step name: %s", this.getNewStepName());
                previousStep.setName(this.getNewStepName() != null ? this.getNewStepName().trim() : "N/A");
                logger.debugf("Merged step name: %s", previousStep.getName());

                // Copy rates from the merged step
                previousStep.setMergeBelowBucketRate(pendingActionStep.getMergeBelowBucketRate());
                previousStep.setDuplicateAboveBucketRate(pendingActionStep.getDuplicateAboveBucketRate());

                // Setting the correct capabilities from the merged step
                previousStep.setCanMergePrevious(pendingActionStep.isCanMergePrevious());
                previousStep.setCanDuplicate(pendingActionStep.isCanDuplicate());
                previousStep.setCanDelete(pendingActionStep.isCanDelete());

                // Remove the current step
                chosenProcessSteps.remove(pendingActionStep);

                // Update order numbers
                updateOrderNumbers();
            }

            String sMsg = String.format("Process step: %s has been successfully merged into: %s.", pendingActionStep.getName(), this.getMergeTargetStepName());
            FacesMessage fMsg = new FacesMessage(FacesMessage.SEVERITY_INFO, "Step merged", sMsg);

            // Clear the pending action step
            this.setPendingActionStep(null);
            this.setMergeTargetStepName(null);
            this.setNewStepName(null);

            // Show success message
            FacesContext.getCurrentInstance().addMessage(null, fMsg);

            // Update the UI
            PrimeFaces.current().ajax().update("processShowForm:timelinePanel");
//            PrimeFaces.current().ajax().update("processShowForm:processShowCard");
        }
    }
    public void executeDuplicate() {
        logger.infof("Duplicate action executed for step: %s", this.getPendingActionStep().getName());
        // TODO: Implement duplicate logic here
        // to update some database state or perform other actions
        if (pendingActionStep != null && pendingActionStep.getName() != null && !pendingActionStep.getName().trim().isEmpty() && newStepName != null && !newStepName.trim().isEmpty()) {
            logger.infof("Execution DUPLICATE for step: %s with new name: %s", pendingActionStep.getName(), this.getNewStepName());

            // TODO: Implement actual duplication logic
            // For now, just duplicate the step
            // 1. Create a new step with the new name
            // 2. Insert it into the list after the current step
            // 3. Update the order numbers of the subsequent steps
            // 4. Update the database

            // Create duplicate step
            ProcessStep duplicateStep = new ProcessStep();
            duplicateStep.setName(this.getNewStepName().trim());
            duplicateStep.setStepId("step_duplicate_" + pendingActionStep.getStepId() + "_" + duplicateStep.getName().hashCode());
            duplicateStep.setCanMergePrevious(true);
            duplicateStep.setCanDuplicate(true);
            duplicateStep.setCanDelete(true);
            duplicateStep.setMergeBelowBucketRate(pendingActionStep.getMergeBelowBucketRate());
            duplicateStep.setDuplicateAboveBucketRate(pendingActionStep.getDuplicateAboveBucketRate());

            // Update the original step name
            pendingActionStep.setName(this.getOriginalStepName().trim());

            // Insert the duplicate step after the current step
            int currentIndex = chosenProcessSteps.indexOf(pendingActionStep);
            chosenProcessSteps.add(currentIndex + 1, duplicateStep);
            updateOrderNumbers();

            String sMsg = String.format("Process step: %s has been successfully duplicated as: %s.", pendingActionStep.getName(), duplicateStep.getName());
            FacesMessage fMsg = new FacesMessage(FacesMessage.SEVERITY_INFO, "Step duplicated", sMsg);

            // Clear the pending action step
            this.pendingActionStep.setDoDuplicate(false);
            this.setPendingActionStep(null);
            this.setOriginalStepName(null);
            this.setMergeTargetStepName(null);
            this.setNewStepName(null);

            // Show success message
            FacesContext.getCurrentInstance().addMessage(null, fMsg);

            // Update the UI
            PrimeFaces.current().ajax().update("processShowForm:timelinePanel");
//            PrimeFaces.current().ajax().update("processShowForm:processShowCard");
        }
    }
    public void executeDelete() {
        logger.infof("Delete action executed for step: %s", this.getPendingActionStep().getName());

        // TODO: Implement delete logic here
        if (pendingActionStep != null) {
            logger.infof("Execution DELETE for step: %s", pendingActionStep.getName());

            // TODO: Implement actual deletion logic
            // For now, just remove from the list
            // 1. Remove the step from the process
            // 2. Update the order numbers of the subsequent steps
            // 3. Update the database

            // Remove from the current list
            this.chosenProcessSteps.remove(pendingActionStep);
            updateOrderNumbers();

            String sMsg = String.format("Process step: %s has been successfully deleted.", pendingActionStep.getName());
            FacesMessage fMsg = new FacesMessage(FacesMessage.SEVERITY_INFO, "Step deleted", sMsg);

            // Clear the pending action step
            this.setPendingActionStep(null);

            // Show success message
            FacesContext.getCurrentInstance().addMessage(null, fMsg);

            // Update the UI
            PrimeFaces.current().ajax().update("processShowForm:timelinePanel");
//            PrimeFaces.current().ajax().update("processShowForm:processShowCard");
        }
    }

    // Cancel methods for each action to handle canceling the confirmation dialogs
    public void cancelMerge() {
        logger.infof("Merge action cancelled for step: %s", this.getPendingActionStep().getName());
        // Clear the pending action step
        if (pendingActionStep != null) {
            pendingActionStep.setDoMerge(false);
        }
        this.setPendingActionStep(null);
        this.setMergeTargetStepName(null);
        this.setNewStepName(null);
    }
    public void cancelDuplicate() {
        logger.infof("Duplicate action cancelled for step: %s", this.getPendingActionStep().getName());
        // Clear the pending action step
        if (pendingActionStep != null) {
            pendingActionStep.setDoDuplicate(false);
        }
        this.setPendingActionStep(null);
        this.setOriginalStepName(null);
        this.setNewStepName(null);
    }
    public void cancelDelete() {
        logger.infof("Delete action cancelled for step: %s", this.getPendingActionStep().getName());
        // Clear the pending action step
        if (pendingActionStep != null) {
            pendingActionStep.setDoDelete(false);
        }
        this.setPendingActionStep(null);
    }

    // Helper method to update order numbers after modification
    private void updateOrderNumbers() {
        if (!chosenProcessSteps.isEmpty()) chosenProcessSteps.getFirst().setCanMergePrevious(false);
        for (int i = 0; i < chosenProcessSteps.size(); i++) {
            chosenProcessSteps.get(i).setOrderNumber(i + 1);
            logger.debugf("Updated order number %d - %s, %s", chosenProcessSteps.get(i).getOrderNumber(), chosenProcessSteps.get(i).getName(), chosenProcessSteps.get(i).getStepId());
        }
    }

    public void forceUpdate() {
        logger.info("> Force DB reload for Processes.");

        // Reset all controller state
        selectedProcessType = null;
        selectedJetSizeKind = JetSizeKind.LARGE;  // Reset to default
        processesForSelectedType = new ArrayList<>();
        processChoices = new ArrayList<>();
        chosenProcessSteps = new ArrayList<>();

        // Force reload from DB
        processService.loadProcesses();

        // Reload process choices for the default jet size
        loadProcessTypeChoices();

        // Force UI refresh by requesting an update of key components
        PrimeFaces.current().ajax().update("processShowForm:processShowCard");

        logger.info("< Force DB reload for Processes.");
    }

    // Getter for displaying purposes - shows the selected procss type info
    public String getSelectedProcessTypeInfo() {
        if (selectedProcessType != null && !processesForSelectedType.isEmpty()) {
            return String.format("Process Type: %s (%d process steps)", selectedProcessType, processesForSelectedType.size());
        }
        return null;
    }

    // Inner class to represent a process step for the timeline
    @Getter @Setter @ToString
    public static class ProcessStep implements Serializable {
        private String stepId; // Unique identifier for this step
        private String name;
        private int orderNumber;
        private boolean canMergePrevious;
        private boolean canDuplicate;
        private boolean canDelete;
        private int mergeBelowBucketRate;
        private int duplicateAboveBucketRate;

        // Individual checkbox states for each step
        private boolean doMerge = false;
        private boolean doDuplicate = false;
        private boolean doDelete = false;

    }
}