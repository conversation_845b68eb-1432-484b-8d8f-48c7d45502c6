package unite.nz.hamiltonjet.controllers.process;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.jboss.logging.Logger;
import unite.nz.hamiltonjet.service.process.SlotboardProcessService;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ProcessesControllerTest {

    @Mock
    private Logger logger;

    @Mock
    private SlotboardProcessService processService;

    @InjectMocks
    private ProcessesController controller;

    @BeforeEach
    void setUp() {
        // Initialize the controller's list
        controller.setChosenProcessSteps(new ArrayList<>());
    }

    @Test
    void testMergeStepNameCalculation() {
        // Create test steps
        ProcessesController.ProcessStep step1 = new ProcessesController.ProcessStep();
        step1.setStepId("step_1");
        step1.setName("WELD");
        step1.setOrderNumber(1);
        step1.setCanMergePrevious(false);

        ProcessesController.ProcessStep step2 = new ProcessesController.ProcessStep();
        step2.setStepId("step_2");
        step2.setName("WELD (Copy)");
        step2.setOrderNumber(2);
        step2.setCanMergePrevious(true);
        step2.setDoMerge(true);

        // Add steps to the controller
        List<ProcessesController.ProcessStep> steps = new ArrayList<>();
        steps.add(step1);
        steps.add(step2);
        controller.setChosenProcessSteps(steps);

        // Test the confirmMerge method
        controller.confirmMerge(step2);

        // Verify that the names are set correctly
        assertEquals("WELD", controller.getMergeTargetStepName());
        assertEquals("WELD & WELD (Copy)", controller.getNewStepName());
        assertEquals("WELD & WELD (Copy)", controller.getDialogNewStepName());
        assertEquals(step2, controller.getPendingActionStep());
    }

    @Test
    void testExecuteMergeWithOriginalCalculatedName() {
        // Setup the controller state as if confirmMerge was called
        ProcessesController.ProcessStep step1 = new ProcessesController.ProcessStep();
        step1.setStepId("step_1");
        step1.setName("WELD");
        step1.setOrderNumber(1);

        ProcessesController.ProcessStep step2 = new ProcessesController.ProcessStep();
        step2.setStepId("step_2");
        step2.setName("WELD (Copy)");
        step2.setOrderNumber(2);

        List<ProcessesController.ProcessStep> steps = new ArrayList<>();
        steps.add(step1);
        steps.add(step2);
        controller.setChosenProcessSteps(steps);

        controller.setPendingActionStep(step2);
        controller.setMergeTargetStepName("WELD");
        controller.setNewStepName("WELD & WELD (Copy)");
        controller.setDialogNewStepName("WELD & WELD (Copy)");

        // Execute the merge
        controller.executeMerge();

        // Verify the merge was successful
        assertEquals(1, controller.getChosenProcessSteps().size());
        assertEquals("WELD & WELD (Copy)", controller.getChosenProcessSteps().get(0).getName());
        
        // Verify cleanup
        assertNull(controller.getPendingActionStep());
        assertNull(controller.getMergeTargetStepName());
        assertNull(controller.getNewStepName());
        assertNull(controller.getDialogNewStepName());
    }

    @Test
    void testExecuteMergeWithModifiedDialogName() {
        // Setup the controller state as if confirmMerge was called and user modified the name
        ProcessesController.ProcessStep step1 = new ProcessesController.ProcessStep();
        step1.setStepId("step_1");
        step1.setName("WELD");
        step1.setOrderNumber(1);

        ProcessesController.ProcessStep step2 = new ProcessesController.ProcessStep();
        step2.setStepId("step_2");
        step2.setName("WELD (Copy)");
        step2.setOrderNumber(2);

        List<ProcessesController.ProcessStep> steps = new ArrayList<>();
        steps.add(step1);
        steps.add(step2);
        controller.setChosenProcessSteps(steps);

        controller.setPendingActionStep(step2);
        controller.setMergeTargetStepName("WELD");
        controller.setNewStepName("WELD & WELD (Copy)");
        // Simulate user modifying the dialog input
        controller.setDialogNewStepName("Custom Merged Step Name");

        // Execute the merge
        controller.executeMerge();

        // Verify the merge used the dialog name
        assertEquals(1, controller.getChosenProcessSteps().size());
        assertEquals("Custom Merged Step Name", controller.getChosenProcessSteps().get(0).getName());
        
        // Verify cleanup
        assertNull(controller.getPendingActionStep());
        assertNull(controller.getMergeTargetStepName());
        assertNull(controller.getNewStepName());
        assertNull(controller.getDialogNewStepName());
    }

    @Test
    void testCancelMerge() {
        // Setup pending merge
        ProcessesController.ProcessStep step = new ProcessesController.ProcessStep();
        step.setDoMerge(true);
        
        controller.setPendingActionStep(step);
        controller.setMergeTargetStepName("WELD");
        controller.setNewStepName("WELD & WELD (Copy)");
        controller.setDialogNewStepName("WELD & WELD (Copy)");

        // Cancel the merge
        controller.cancelMerge();

        // Verify cleanup
        assertFalse(step.isDoMerge());
        assertNull(controller.getPendingActionStep());
        assertNull(controller.getMergeTargetStepName());
        assertNull(controller.getNewStepName());
        assertNull(controller.getDialogNewStepName());
    }
}
